@push('styles')
    <style>
        #box-layout {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            gap: 10px;
            /* background-color: red; */
            width: 90vw;
            height: 70vh;
        }

        .box-column {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .box-kotak {
            flex: 1;
            background-color: #ffcc57;
            border-radius: 20px;
        }

        .box-kotak .box-title {
            color: #221452;
        }

            

    </style>
@endpush


<div class="min-h-screen flex flex-col justify-center px-8 py-0 lg:px-20 gap-0 ">
    <h1
        class="glowing-title text-4xl sm:text-5xl xl:text-6xl font-bold font-fors text-white mb-2 text-left self-start">
        Rules
    </h1>
    <h2 class="glowing-title text-lg lg:text-2xl font-medium font-fors text-white text-left self-start">
        Make sure we follow these rules so we all can have a good time
    </h2>

    {{-- div layout untuk pasal dan download file --}}

    <div class="flex flex-col mt-8 gap-0.5 ">

        {{-- div layout pasal --}}
        <div class="grid grid-cols-2 md:grid-cols-2  xl:grid-cols-4 gap-2 p-0 md:p-1 w-full">
            <div class="box-column">
                {{-- box aturan 1 --}}
                <div onclick="" id="box-1"
                    class="box-kotak p-4 flex flex-col h-full hover:shadow-[#fff] hover:shadow-[0_0px_15px_rgba(0,0,0,0.3)] hover:cursor-pointer transition-shadow duration-300 ease-in-out min-h-[120px] xl:min-h-[220px]"
                    data-aos="fade-down-right" data-aos-delay="700">
                    <h1 class="box-title font-bold font-fors text-xl lg:text-4xl mb-0 md:text-2xl"></h1>
                    <p class="box-description neonText text-xs text-2md sm:text-lg"></p>
                    <div class="flex justify-end mt-auto">
                        <div
                            class="icon-container border-2 rounded-full w-7 h-7 xl:w-10 xl:h-10 md:w-12 md:h-12 flex items-center justify-center border-[#5e2e6a] text-[#ffcc57] bg-[#5e2e6a] hover:bg-[#ffcc57] hover:text-[#5e2e6a] hover:border-[#5e2e6a] transition-colors duration-200">
                            <button class="text-xl md:text-3xl hover:cursor-pointer">
                                <i class="fa-solid fa-arrow-right"></i>
                            </button>
                        </div>
                    </div>
                </div>

                {{-- box aturan 2 --}}
                <div onclick="" id="box-2"
                    class="box-kotak p-4 flex flex-col h-full hover:shadow-[#fff] hover:shadow-[0_0px_15px_rgba(0,0,0,0.3)] hover:cursor-pointer transition-shadow duration-300 ease-in-out min-h-[120px] xl:min-h-[220px]"
                    data-aos="fade-up-right"data-aos-delay="700">
                    <h1 class="box-title font-bold font-fors text-xl lg:text-4xl mb-0 md:text-2xl"></h1>
                    <p class="box-description neonText text-xs text-2md sm:text-lg"></p>
                    <div class="flex justify-end mt-auto">
                        <div
                            class="icon-container border-2 rounded-full w-7 h-7 xl:w-10 xl:h-10 md:w-12 md:h-12 flex items-center justify-center border-[#5e2e6a] text-[#ffcc57] bg-[#5e2e6a] hover:bg-[#ffcc57] hover:text-[#5e2e6a] hover:border-[#5e2e6a] transition-colors duration-200">
                            <button class="text-xl md:text-3xl hover:cursor-pointer">
                                <i class="fa-solid fa-arrow-right"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="box-column">
                {{-- box aturan 3 --}}
                <div onclick="" id="box-3"
                    class="box-kotak p-4 flex flex-col h-full hover:shadow-[#fff] hover:shadow-[0_0px_15px_rgba(0,0,0,0.3)] hover:cursor-pointer transition-shadow duration-300 ease-in-out min-h-[120px] xl:min-h-[220px]"
                    data-aos="fade-down" data-aos-delay="700">
                    <h1 class="box-title font-bold font-fors text-xl lg:text-4xl mb-0 md:text-2xl"></h1>
                    <p class="box-description neonText text-xs text-2md sm:text-lg"></p>
                    <div class="flex justify-end mt-auto">
                        <div
                            class="icon-container border-2 rounded-full w-7 h-7 xl:w-10 xl:h-10 md:w-12 md:h-12 flex items-center justify-center border-[#5e2e6a] text-[#ffcc57] bg-[#5e2e6a] hover:bg-[#ffcc57] hover:text-[#5e2e6a] hover:border-[#5e2e6a] transition-colors duration-200">
                            <button class="text-xl md:text-3xl hover:cursor-pointer">
                                <i class="fa-solid fa-arrow-right"></i>
                            </button>
                        </div>
                    </div>
                </div>

                {{-- box aturan 4 --}}
                <div onclick="" id="box-4"
                    class="box-kotak p-4 flex flex-col h-full hover:shadow-[#fff] hover:shadow-[0_0px_15px_rgba(0,0,0,0.3)] hover:cursor-pointer transition-shadow duration-300 ease-in-out min-h-[120px] xl:min-h-[220px]"
                    data-aos="fade-up" data-aos-delay="700">
                    <h1 class="box-title font-bold font-fors text-xl lg:text-4xl mb-0 md:text-2xl"></h1>
                    <p class="box-description neonText text-xs text-2md sm:text-lg"></p>
                    <div class="flex justify-end mt-auto">
                        <div
                            class="icon-container border-2 rounded-full w-7 h-7 xl:w-10 xl:h-10 md:w-12 md:h-12 flex items-center justify-center border-[#5e2e6a] text-[#ffcc57] bg-[#5e2e6a] hover:bg-[#ffcc57] hover:text-[#5e2e6a] hover:border-[#5e2e6a] transition-colors duration-200">
                            <button class="text-xl md:text-3xl hover:cursor-pointer">
                                <i class="fa-solid fa-arrow-right"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="box-column">
                {{-- box aturan 5 --}}
                <div onclick="" id="box-5"
                    class="box-kotak p-4 flex flex-col h-full hover:shadow-[#fff] hover:shadow-[0_0px_15px_rgba(0,0,0,0.3)] hover:cursor-pointer transition-shadow duration-300 ease-in-out min-h-[120px] xl:min-h-[220px]"
                    data-aos="fade-down" data-aos-delay="700">
                    <h1 class="box-title font-bold font-fors text-xl lg:text-4xl mb-0 md:text-2xl"></h1>
                    <p class="box-description neonText text-xs text-2md sm:text-lg"></p>
                    <div class="flex justify-end mt-auto">
                        <div
                            class="icon-container border-2 rounded-full w-7 h-7 xl:w-10 xl:h-10 md:w-12 md:h-12 flex items-center justify-center border-[#5e2e6a] text-[#ffcc57] bg-[#5e2e6a] hover:bg-[#ffcc57] hover:text-[#5e2e6a] hover:border-[#5e2e6a] transition-colors duration-200">
                            <button class="text-xl md:text-3xl hover:cursor-pointer">
                                <i class="fa-solid fa-arrow-right"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="box-column">
                {{-- box aturan 6 --}}
                <div onclick="" id="box-6"
                    class="box-kotak p-4 flex flex-col h-full hover:shadow-[#fff] hover:shadow-[0_0px_15px_rgba(0,0,0,0.3)] hover:cursor-pointer transition-shadow duration-300 ease-in-out min-h-[120px] xl:min-h-[220px]"
                    data-aos="fade-down-left" data-aos-delay="700">
                    <h1 class="box-title font-bold font-fors text-xl lg:text-4xl mb-0 md:text-2xl"></h1>
                    <p class="box-description neonText text-xs text-2md sm:text-lg"></p>
                    <div class="flex justify-end mt-auto">
                        <div
                            class="icon-container border-2 rounded-full w-7 h-7 xl:w-10 xl:h-10 md:w-12 md:h-12 flex items-center justify-center border-[#5e2e6a] text-[#ffcc57] bg-[#5e2e6a] hover:bg-[#ffcc57] hover:text-[#5e2e6a] hover:border-[#5e2e6a] transition-colors duration-200">
                            <button class="text-xl md:text-3xl hover:cursor-pointer">
                                <i class="fa-solid fa-arrow-right"></i>
                            </button>
                        </div>
                    </div>
                </div>

                {{-- box aturan 7 --}}
                <div onclick="" id="box-7"
                    class="box-kotak p-4 flex flex-col h-full hover:shadow-[#fff] hover:shadow-[0_0px_15px_rgba(0,0,0,0.3)] hover:cursor-pointer transition-shadow duration-300 ease-in-out min-h-[120px] xl:min-h-[220px]"
                    data-aos="fade-down-left" data-aos-delay="700">
                    <h1 class="box-title font-bold font-fors text-xl lg:text-4xl mb-0 md:text-2xl"></h1>
                    <p class="box-description neonText text-xs text-2md sm:text-lg"></p>
                    <div class="flex justify-end mt-auto">
                        <div
                            class="icon-container border-2 rounded-full w-7 h-7 xl:w-10 xl:h-10 md:w-12 md:h-12 flex items-center justify-center border-[#5e2e6a] text-[#ffcc57] bg-[#5e2e6a] hover:bg-[#ffcc57] hover:text-[#5e2e6a] hover:border-[#5e2e6a] transition-colors duration-200">
                            <button class="text-xl md:text-3xl hover:cursor-pointer">
                                <i class="fa-solid fa-arrow-right"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        {{-- div layout download --}}
        <div class="flex flex-col w-full items-stretch mt-2 " data-aos="fade-up" data-aos-delay="700">
            <div
                class="w-full h-full text-[#221452] bg-[#f7e99e] hover:bg-[#5e2e6a] hover:text-white hover:cursor-pointer rounded-lg p-4 flex flex-col justify-between text-center transition-colors duration-200  ">
                <a href="{{ asset('assets/pdf/PERATURAN PESERTA WGG 2025.pdf') }}" download
                    class="box-title font-bold font-fors text-2xl lg:text-3xl">
                    Download full PDF
                </a>
            </div>
        </div>

    </div>
</div>

{{-- modal untuk pasal --}}
<div id="modal-rules-info"
    class="fixed inset-0 bg-black/40 backdrop-blur-sm flex items-center justify-center p-4
           transition-opacity duration-300 opacity-0 pointer-events-none"
    data-lenis-prevent="true"
    onclick="closeModalRules(event)">
    <div class="w-full max-w-md rounded-lg p-6 relative shadow-lg text-[#221452] lg:min-w-xl xl:min-h-[65vh] xl:min-w-[95vw] max-h-[70vh] xl:max-h-[70vh]
        flex flex-col
        transform transition-all duration-700 ease-out -translate-y-10 scale-95 opacity-0"
        style="background: #FFCC57; background: linear-gradient(218deg, rgba(255, 204, 87, 1) 10%, rgba(247, 233, 158, 1) 69%);"
        onclick="event.stopPropagation()">

        <h2 id="modal-rules-title" class="text-2xl font-bold mb-4 text-left text-bold lg:text-5xl font-fors"></h2>
        <p id="modal-rules-content-ayat"
            class="text-sm lg:text-xl text-gray-700 leading-relaxed flex-grow overflow-y-auto font-fors max-h-[calc(100vh-180px)] px-8">
        </p>

        <div class="flex justify-center mt-4">
            <button onclick="closeModalRules()"
                class="bg-[#e1005e] font-bold text-white px-10 py-2 text-lg rounded-full hover:bg-[#c20054] transition duration-300 hover:cursor-pointer font-fors">
                Close
            </button>
        </div>
    </div>
</div>


@push('scripts')
    <script>
        const pasalAyat = @json($pasalAyat);

        // Dapatkan referensi elemen modal di luar fungsi agar tidak perlu mencarinya berulang kali
        const modalRulesInfo = document.getElementById('modal-rules-info');
        const modalContent = modalRulesInfo.querySelector('.w-full.max-w-md'); // Kontainer modal bagian dalamnya

        window.addEventListener("DOMContentLoaded", () => {
            pasalAyat.forEach((item, index) => {
                const box = document.getElementById(`box-${index + 1}`);
                if (!box) return;

                // Set onclick
                box.setAttribute("onclick", `openModalRules('${item.id}')`); // Mengganti ke openModalRules

                // Set judul & keterangan
                box.querySelector(".box-title").innerText = `Pasal ${item.pasal}`;
                box.querySelector(".box-description").innerText = item.keterangan;
            });
            modalContent.addEventListener('click', (e) => {
                e.stopPropagation();
            });
        });

        function openModalRules(id) {
            const data = pasalAyat.find(item => item.id === id);
            if (!data) return;

            const modalRulesTitle = document.getElementById("modal-rules-title");
            const modalRulesContentAyat = document.getElementById('modal-rules-content-ayat');

            modalRulesTitle.innerText = `Pasal ${data.pasal}: ${data.keterangan}`;

            let htmlAyat = '';

            if (data.ayat.length === 1) {
                htmlAyat = `<p class="text-left">${data.ayat[0].ayat}</p>`;
            } else {
                htmlAyat = '<ul class="list-decimal pl-5 text-left space-y-2">';
                data.ayat.forEach(item => {
                    htmlAyat += `<li>${item.ayat}</li>`;
                });
                htmlAyat += '</ul>';
            }

            modalRulesContentAyat.innerHTML = htmlAyat;

            // transisi masuk
            modalRulesInfo.classList.remove('hidden');

            //requestAnimationFrame untuk memastikan browser telah menerapkan perubahan display
            requestAnimationFrame(() => {
                // Hapus kelas-kelas yang membuat overlay tidak terlihat
                modalRulesInfo.classList.remove('opacity-0', 'pointer-events-none');
                modalContent.classList.remove('-translate-y-10', 'scale-95', 'opacity-0');
            });

            // mencegah scroll body saat modal terbuka
            document.documentElement.classList.add('overflow-hidden'); // <html>
            document.body.classList.add('overflow-hidden');  
            
            if (window.lenis) {
                window.lenis.stop();
            }

        
        }

        function closeModalRules(event) {
            // Mencegah penutupan modal ketika mengklik di dalam konten modal itu sendiri
            if (event && modalContent.contains(event.target) && event.target !== modalRulesInfo) {
                return;
            }

            //  overlay transparan dan konten modal kembali ke kondisi awal tersembunyi
            modalRulesInfo.classList.add('opacity-0', 'pointer-events-none');
            modalContent.classList.add('-translate-y-10', 'scale-95', 'opacity-0');
            setTimeout(() => {
                modalRulesInfo.classList.add('hidden');
                document.documentElement.classList.remove('overflow-hidden');
                document.body.classList.remove('overflow-hidden');
            }, 700);

            if (window.lenis) {
                window.lenis.start();
            }

        }
    </script>
@endpush
