<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class MainController extends Controller
{
    function landing()
    {

        // get required data for landing
        $pasalAyatUrl = env('API_URL') . '/pasal-ayat';
        $pasalAyatResponse = Http::get($pasalAyatUrl);

        $faqUrl = env('API_FAQ_URL');
        $faqResponse = Http::get($faqUrl);

        $dateUrl = env('API_URL') . '/wggdates';
        $dateResponse = Http::get($dateUrl);



        if ($pasalAyatResponse->failed()) {
            // terserah mau diformat/dihandle gimana datanya
            // $pasalAyat = 
        } else {
            $pasalAyat = $pasalAyatResponse->json();
        }

        if ($faqResponse->successful()) {
            $faq = $faqResponse->json()['data'];
        } else {
            return redirect()->back()->with('error', 'Failed to fetch faq data');
        }

        if ($dateResponse->successful()) {
            $rawDates = $dateResponse->json()['data'];
            $grouped = [];

            foreach ($rawDates as $event) {
                $baseName = preg_replace('/\s*-\s*day\s*\d+/i', '', $event['event_name']);

                $grouped[$baseName][] = $event['event_date'];
            }

            $images = [
                'sarasehan.png',
                'padi.png',
                'bendera.png',
                'festival.png',
                'search.png',
                'kupukupu.png',
                'padi.png'
            ];

            $dresscode = [
                '-',
                'Light-colored shirt and black pants',
                'Dark-colored shirt and black pants',
                'PCU t-shirt and black pants',
                'Faculty t-shirt and black pants',
                'Light-colored shirt and black pants'
            ];

            $cards = [];
            $i = 0;
            foreach ($grouped as $baseName => $dates) {
                $formattedDates = [];

                foreach ($dates as $dateStr) {
                    $dateObj = \Carbon\Carbon::parse($dateStr);
                    $day = $dateObj->day;
                    $suffix = ($day % 10 == 1 && $day != 11) ? 'st' : (($day % 10 == 2 && $day != 12) ? 'nd' : (($day % 10 == 3 && $day != 13) ? 'rd' : 'th'));
                    $formattedDates[] = $day . $suffix;
                    $monthYear = $dateObj->format('F, Y');
                }

                $finalDate = implode(' & ', $formattedDates) . ' of ' . $monthYear;

                $cards[] = [
                    'id' => $i + 1,
                    'title' => $baseName,
                    'content' => $finalDate,
                    'image' => asset('assets/timeline/' . $images[$i]),
                    'modalTitle' => $baseName,
                    'modalContent' => "Date: $finalDate <br> Time: 08.30 - 16.00 WIB <br> Dresscode: " . $dresscode[$i],
                ];
                $i++;
            }
        } else {
            return redirect()->back()->with('error', 'Failed to fetch wgg dates');
        }


        $data['title'] = 'Home';
        $data['pasalAyat'] = $pasalAyat;
        $data['faqs'] = $faq;
        $data['dates'] = $cards;

        return view('landing', $data);
    }

    function viewBriefing()
    {
        $data['title'] = "Forms & Briefing";
        $data['is_form'] = true;
        return view('form-briefing', $data);
    }
}
