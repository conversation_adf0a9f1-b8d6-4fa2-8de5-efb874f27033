@push('styles')
    <style>
        .neonText {
            color: #fff;
            text-shadow:
                0 0 4px rgba(255, 255, 255, 0.7),
                0 0 7px rgba(230, 240, 246, 0.5),
                0 0 12px rgba(189, 212, 227, 0.4),
                0 0 20px rgba(159, 204, 209, 0.6),
                0 0 30px rgba(159, 204, 209, 0.5);
        }

        @media (width: 1024px) and (height: 600px) {
            .shuttle-grid {
                display: grid;
                grid-template-columns: repeat(2, minmax(0, 1fr));
            }

            .shuttle-title {
                font-size: 3rem;
            }

            .shuttle-subtext {
                font-size: 1.25rem;
            }

            .shuttle-button-text {
                font-size: 1rem;
            }

            .shuttle-section {
                gap: 1rem !important;
            }

            .shuttle-button {
                gap: 0.75rem !important;
            }

            .shuttle-img {
                width: 100% !important;
            }
        }

        @media (width:912px) and (height:1368px) {
            .shuttle-title {
                font-size: 4.5rem;
            }

            .shuttle-subtext {
                font-size: 1.875rem;
            }

            .shuttle-button-text {
                font-size: 1.5rem;
            }

            .shuttle-img {
                width: 80% !important;
            }
        }

        @media (width:540px) and (height:720px) {
            .shuttle-container {
                gap: 1.875rem;
            }

            .shuttle-img {
                width: 90% !important;
            }

            .shuttle-button {
                gap: 1rem;
            }

            .shuttle-grid {
                gap: 1.875rem;
            }
        }

        @media (max-width:375px) and (max-height:667px) {
            .shuttle-title {
                font-size: 1.875rem !important;
            }

            .shuttle-button-text {
                font-size: 1rem;
            }
        }

        @media only screen and (min-device-width: 375px) and (max-device-width: 375px) and (min-device-height: 667px) and (max-device-height: 667px) {
            .shuttle-title {
                font-size: 2rem !important;
            }

            .shuttle-container {
                gap: 1.5rem;
            }

            .shuttle-grid {
                gap: 1.5rem;
            }

            .shuttle-subtext {
                font-size: 1rem !important;
            }

            .shuttle-button-text {
                font-size: 0.875rem !important;
            }

            .shuttle-button {
                gap: 1rem !important;
            }

            .shuttle-section {
                gap: 1rem !important;
            }

            .shuttle-img {
                width: 90% !important;
            }
        }

        @media (min-width: 850px) and (max-width: 860px) and (min-height: 1270px) and (max-height: 1290px) {
            .shuttle-title {
                font-size: 4.275rem !important;
            }

            .shuttle-subtext {
                font-size: 1.875rem;
            }

            .shuttle-button-text {
                font-size: 1.5rem;
            }

            .shuttle-img {
                width: 90%;
            }
        }

        .bus-hidden {
            opacity: 0;
            transform: translateX(-400px);
        }

        .door-closed {
            transform: rotateY(-90deg);
        }
    </style>
@endpush

<div
    class="shuttle-container min-h-screen flex flex-col justify-center gap-12 md:gap-12 lg:gap-15 xl:gap-20
           max-w-full px-8 lg:px-20 px-auto">
    <h1 class="glowing-title text-white shuttle-title text-4xl sm:text-5xl xl:text-6xl font-bold font-fors text-white z-[20]"
        data-aos="fade-right" data-aos-duration="500" data-aos-easing="ease-out-cubic">
        Petra Shuttle
        Bus</h1>
    <div class="shuttle-grid grid grid-cols-1 lg:grid-cols-2 gap-12 md:gap-12 xl:gap-10">
        <div class="flex flex-col items-center lg:items-start">
            <div class="relative shuttle-img w-8/12 max-sm:w-10/12 lg:w-[95%] lg:mt-6 z-[0]" id="shuttleImage">
                <img src="{{ asset('assets/shuttle-bus/bus.png') }}" class="w-full h-auto z-[1]" id="shuttleBusBase">

                <img src="{{ asset('assets/shuttle-bus/pintu.png') }}" class="absolute top-0 left-0 w-full h-auto z-[2]"
                    id="shuttleDoor">

                <img src="{{ asset('assets/shuttle-bus/bagiandepanbus.png') }}"
                    class="absolute top-0 left-0 w-full h-auto z-[3]" id="shuttleFront">
            </div>
        </div>
        <div class="shuttle-section flex flex-col justify-center items-center lg:items-start gap-7 md:gap-10 lg:gap-9">
            <h2 class="glowing-title text-white text-center lg:text-start shuttle-subtext sm:text-lg md:text-2xl font-medium font-fors"
                data-aos="fade-down" data-aos-duration="900" data-aos-delay="200" data-aos-easing="ease-out-cubic">
                Looking for a ride from East and West of Surabaya?<br>
                Petra Shuttle Bus got you covered!<br>
                The shuttle bus service is available during WGG.<br>
            </h2>
            <div class="flex flex-col justify-center items-center lg:items-start">
                <h2 class="glowing-title text-white text-center lg:text-start shuttle-subtext sm:text-lg md:text-2xl font-medium font-fors mb-4"
                    data-aos="fade-down" data-aos-duration="900" data-aos-delay="200" data-aos-easing="ease-out-cubic">
                    Download the app and guide to find out more!
                </h2>
                <div>
                    <div
                        class="shuttle-button grid grid-cols-1 md:grid-cols-2 lg:grid-cols-1 gap-4 md:gap-6 lg:gap-6 xl:gap-5 max-w-[500px]">
                        <div data-aos="fade-down-right" data-aos-duration="700" data-aos-delay="400">
                            <button onclick=redirectToAppStore()
                                class="w-full rounded-lg px-[14px] py-[4px] cursor-pointer transition-colors duration-300 group bg-[var(--pink)] border-[3px] border-[var(--purple)] hover:cursor-pointer hover:bg-[var(--purple)]">
                                <span
                                    class="shuttle-button-text relative -top-[2px] sm:-top-[1px] text-lg max-sm:text-sm md:text-lg font-bold text-white font-fors">Get
                                    the App!</span>
                            </button>
                        </div>
                        <div data-aos-duration="700" data-aos-delay="550" data-aos="fade-up-left">
                            <a href="{{ asset('assets/pdf/PETRA SHUTTLE BUS.pdf') }} " download>
                                <button
                                    class="w-full rounded-lg px-[14px] py-[4px] bg-[var(--purple)] border-[3px] border-[var(--purple)]
                                    cursor-pointer !transition-colors !duration-300 group hover:cursor-pointer hover:border-[var(--purple)] hover:bg-[var(--pink)]">
                                    <span
                                        class="shuttle-button-text relative -top-[2px] sm:-top-[1px] max-sm:text-sm md:text-lg font-bold text-white font-fors">Download
                                    App Guide</span>
                                </button>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            gsap.registerPlugin(ScrollTrigger);

            const busBase = document.getElementById('shuttleBusBase');
            const busFront = document.getElementById('shuttleFront');
            const busDoor = document.getElementById('shuttleDoor');

            gsap.set([busBase, busFront, busDoor], { 
                x: -400,
                y: -100, 
                visibility: 'visible'
            });
            
            const animateIn = () => {
                const tl = gsap.timeline();
                tl.to([busBase, busFront, busDoor], {
                        duration: 1.5,
                        x: 0,
                        y: 0,
                        ease: "power2.out",
                    })
                    .to(busDoor, {
                        duration: 0.7,
                        ease: 'none',
                        keyframes: [
                            { rotationY: 10 },
                            { rotationY: -8 },
                            { rotationY: 5 },
                            { rotationY: 0 }
                        ],
                        transformOrigin: "20% center" 
                    })
                    .to([busBase, busFront, busDoor], {
                        duration: 0.5,
                        y: -25,
                        ease: "power2.out",
                        yoyo: true,
                        repeat: 1
                    }, "-=0.5"); 
            };

            const animateOut = () => {
                const tl = gsap.timeline();
                tl.to([busBase, busFront, busDoor], {
                    duration: 1,
                    x: -400,
                    y: -100,
                    ease: "power2.in"
                }, "<"); 
            };

            ScrollTrigger.create({
                trigger: "#shuttle",
                start: "top 80%",
                end: "bottom 20%",
                onEnter: animateIn,
                onLeave: animateOut,
                onEnterBack: animateIn,
                onLeaveBack: animateOut,
            });

            let resizeTimer;
            window.addEventListener('resize', () => {
                clearTimeout(resizeTimer);
                resizeTimer = setTimeout(() => {
                    ScrollTrigger.refresh();
                }, 250);
            });
        });

        function redirectToAppStore() {
            const userAgent = navigator.userAgent.toLowerCase();
            
            if (/android/.test(userAgent)) {
                window.open("https://play.google.com/store/apps/details?id=id.ac.petra.shuttlebus&hl=id", "_blank");
            } else if (/iphone|ipad|ipod|mac/.test(userAgent)) {
                window.open("https://apps.apple.com/us/app/petra-shuttle-bus/id1607780115", "_blank");
            } else {
                window.open("https://play.google.com/store/apps/details?id=id.ac.petra.shuttlebus&hl=id", "_blank");
            }
        }

        window.triggerBusAnimation = function() {
            ScrollTrigger.refresh();
        };
    </script>
@endpush
