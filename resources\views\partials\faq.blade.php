@push('styles')
    <style>

        .neonText {
            color: #fff;
            text-shadow:
                0 0 4px rgba(255, 255, 255, 0.7),
                0 0 7px rgba(230, 240, 246, 0.5),
                0 0 12px rgba(189, 212, 227, 0.4),
                0 0 20px rgba(159, 204, 209, 0.6),
                0 0 30px rgba(159, 204, 209, 0.5);
        }

        .faq-container span {
            display: inline-block;
            transition: transform 0.2s ease-in-out;
        }

        .faq-container span.rotate-180 {
            transform: rotate(180deg);
        }

        .collapse-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.4s ease-in-out;
        }

        .collapse-content.show {
            max-height: 5000px !important;
        }

        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: ##ffcc57/80;
            border-radius: 12px;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(to bottom, #a675c9, #4a2b66);
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background-color: #4a2255;
        }

    </style>
    {{-- <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet"> --}}
@endpush



<div class="h-screen flex flex-col justify-center px-8 lg:px-20 gap-8 overflow-y-auto">
    <h1 class="glowing-title text-white text-4xl sm:text-5xl xl:text-6xl font-bold font-fors"
        data-aos="fade-right" data-aos-duration="500" data-aos-easing="ease-out-cubic">Frequently
        Asked Questions</h1>
    <div class="flex flex-col justify-center w-full" data-aos="fade-up" data-aos-duration="700" data-aos-delay="300">
        <div style="border: 2px solid var(--purple)" 
            class="w-full max-w-[1550px] h-[450px] lg:h-[400px] my-4 py-6 lg:py-8 bg-[#ffcc57]/80 rounded-lg flex flex-col items-center overflow-y-auto"
            data-lenis-prevent>
            <div class="w-12/13 max-w-[1400px] accordionExample px-4">
                @foreach ($faqs as $index => $f)
                    <div class="flex flex-col">
                        <div class="py-3 flex flex-col w-full">
                            <h2 id="heading{{ $index }}" class="mb-0 w-full">
                                <button 
                                    class="faq-container transition-colors duration-300 group relative bg-[var(--yellow)] border-[3px] border-[var(--purple)] hover:cursor-pointer hover:bg-[var(--light-yellow)]
                                    flex w-full h-fit items-center justify-between gap-x-5 rounded-lg px-3 md:px-6 py-4 text-left text-base text-neutral-800 transition hover:z-[2] focus:z-[3] focus:outline-none"
                                    type="button" data-twe-collapse-init data-twe-collapse-collapsed
                                    data-twe-target="#collapse{{ $index }}" aria-expanded="false"
                                    aria-controls="collapse{{ $index }}">
                                    <div class="flex flex-row items-center gap-x-3 flex-grow min-w-0">
                                        <h1 class="glowing-title text-white text-4xl font-bold max-sm:text-2xl font-fors drop-shadow-2xl md:w-20 flex-shrink-0"
                                            style="min-width: fit-content;">
                                            Q{{ $index + 1 }}.
                                        </h1>
                                        <p
                                            class="text-xl max-sm:text-base font-fors w-full min-w-0 flex-grow text-wrap">
                                            {{ $f['question'] }}
                                        </p>
                                    </div>
                                    <span class="h-5 w-5 flex-shrink-0">
                                        <img src="{{ asset('assets/panah.png') }}"
                                            class="arrow-icon transition-transform duration-200 ease-in-out rotate-0 h-full w-full object-contain" />
                                    </span>

                                </button>
                            </h2>
                            <div class="collapse-wrapper">
                                <div id="collapse{{ $index }}"
                                    class="collapse-content overflow-hidden text-xl max-sm:text-base font-fors text-gray-800"
                                    data-twe-collapse-item aria-labelledby="heading{{ $index }}">
                                    @foreach ($f['answers'] as $a)
                                        <p class="w-full rounded-lg pb-2 pr-4 pl-5 xl:pl-6 pt-3 break-words">
                                            {{ $a['answer'] }}
                                        </p>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
</div>


@push('scripts')
    {{-- <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script>
        AOS.init();
    </script> --}}
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const buttons = document.querySelectorAll('[data-twe-collapse-init]');

            function expand(element) {
                element.style.display = 'block';
                const height = element.scrollHeight + 'px';
                element.style.height = '0px';

                requestAnimationFrame(() => {
                    element.style.transition = 'height 300ms ease';
                    element.style.height = height;
                });

                function onTransitionEnd() {
                    element.style.height = 'auto';
                    element.removeEventListener('transitionend', onTransitionEnd);
                }
                element.addEventListener('transitionend', onTransitionEnd);

                element.classList.add('show');
            }

            function collapse(element) {
                const height = element.scrollHeight + 'px';
                element.style.height = height;

                requestAnimationFrame(() => {
                    element.style.transition = 'height 300ms ease';
                    element.style.height = '0px';
                });

                function onTransitionEnd() {
                    element.classList.remove('show');
                    element.style.display = 'none';
                    element.style.height = null;
                    element.style.transition = null;
                    element.removeEventListener('transitionend', onTransitionEnd);
                }
                element.addEventListener('transitionend', onTransitionEnd);
            }

            buttons.forEach(button => {
                button.addEventListener('click', () => {
                    const targetSelector = button.getAttribute('data-twe-target');
                    if (!targetSelector) return;

                    const collapseEl = document.querySelector(targetSelector);
                    if (!collapseEl) return;

                    const isExpanded = button.getAttribute('aria-expanded') === 'true';

                    buttons.forEach(otherButton => {
                        const otherTarget = otherButton.getAttribute('data-twe-target');
                        if (!otherTarget || otherTarget === targetSelector) return;

                        const otherCollapseEl = document.querySelector(otherTarget);
                        if (!otherCollapseEl) return;

                        if (otherCollapseEl.classList.contains('show')) {
                            collapse(otherCollapseEl);
                            otherButton.setAttribute('aria-expanded', 'false');

                            const otherArrow = otherButton.querySelector('.arrow-icon');
                            if (otherArrow) otherArrow.classList.remove('rotate-90');
                        }
                    });

                    if (isExpanded) {
                        collapse(collapseEl);
                        button.setAttribute('aria-expanded', 'false');

                        const arrow = button.querySelector('.arrow-icon');
                        if (arrow) arrow.classList.remove('rotate-90');
                    } else {
                        expand(collapseEl);
                        button.setAttribute('aria-expanded', 'true');

                        const arrow = button.querySelector('.arrow-icon');
                        if (arrow) arrow.classList.add('rotate-90');
                    }
                });
            });

        });
    </script>
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
@endpush
