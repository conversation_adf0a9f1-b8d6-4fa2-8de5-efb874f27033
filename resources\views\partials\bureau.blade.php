@push('styles')
    <style>
        .box-bureau {
            background: #ffcc57;
            padding: 20px;
            border-radius: 10px;
            color: #221452;
        }

        .box-bureau:hover {
            box-shadow: 0 0 15px var(--glow);
            cursor: pointer;
        }

        body {
            font-family: 'tt_fors', sans-serif;
            src: url('{{ asset('font/tt_fors.ttf') }}') format('truetype');
        }

        .no-scroll {
            overflow: hidden !important;
            height: 100vh !important;
            width: 100vw !important;
        }
    </style>
@endpush


<div class="flex flex-col min-h-screen w-full ">
    <div class="w-full max-w-full self-start mb-0 px-8 lg:px-20">
        <h1
            class="mb-6 glowing-title text-4xl sm:text-5xl xl:text-6xl font-bold font-fors text-white mb-4 text-left self-start">
            Bureau
        </h1>

        {{-- Grid <PERSON> --}}
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 gap-8">
            <!-- CARD 1 -->
            <div onclick="openModalBureau(1)"
                class="box-bureau w-full mx-auto flex flex-col justify-between p-6 rounded-lg lg:min-w-l xl:min-w-lg  2xl:min-w-xl hover:cursor-pointer transition-shadow duration-300 ease-in-out min-h-[200px] h-full"
                data-aos="fade-down-right">

                <div class="flex-grow">
                    <h1 class="font-bold font-fors text-xl mb-5">Biro Administrasi Akademik (BAAk)</h1>
                    <div class="space-y-4">
                        <div class="flex gap-3 items-center">
                            <div
                                class="icon-container group border-2 rounded-full w-8 h-8 flex items-center justify-center border-[var(--purple)] transition-colors duration-200 hover:bg-[var(--purple)]">
                                <a href="https://www.instagram.com/baa.pcu" target="_blank" aria-label="Instagram BAAK" 
                                    class="transition-[color] duration-[0.3s] ease-[ease] fa-brands fa-instagram text-lg text-[var(--purple)] group-hover:text-[white]">
                                </a>
                            </div>
                            <a href="https://www.instagram.com/baa.pcu" target="_blank"
                                class="text-sm hover:underline text-[var(--purple)] font-fors">@@baa.pcu</a>
                        </div>
                        <div class="flex gap-3 items-center">
                            <div
                                class="icon-container group border-2 rounded-full w-8 h-8 flex items-center justify-center border-[var(--purple)] transition-colors duration-200 hover:bg-[var(--purple)]">
                                <a href="mailto:<EMAIL>" target="_blank" aria-label="Email BAAK"
                                        class="transition-[color] duration-[0.3s] ease-[ease] fa-regular fa-envelope text-lg text-[var(--purple)] group-hover:text-[white]">
                                </a>
                            </div>
                            <a href="mailto:<EMAIL>" target="_blank"
                                class="text-sm hover:underline text-[var(--purple)] font-fors"><EMAIL></a>
                        </div>
                    </div>
                </div>

                <div class="flex justify-end">
                    <button
                        class="relative border-[3px] bg-[#5e2e6a] border-[#5e2e6a] font-bold text-white px-12 py-2 rounded-full transition-colors duration-300 text-sm hover:cursor-pointer hover:border-[#5e2e6a] hover:bg-[#e1005e] font-fors group ">
                        <span class="relative z-10">More Info</span>
                    </button>
                </div>
            </div>

            <!-- CARD 2 -->
            <div onclick="openModalBureau(2)"
                class="box-bureau w-full mx-auto flex flex-col justify-between p-6 rounded-lg lg:min-w-l  xl:min-w-lg  2xl:min-w-xl hover:cursor-pointer transition-shadow duration-300 ease-in-out min-h-[200px] h-full"
                data-aos="fade-down-left">

                <div class="flex-grow">
                    <h1 class="font-bold font-fors text-xl mb-5">Biro Administrasi Keuangan (BAK)</h1>
                    <div class="space-y-4">
                        <div class="flex gap-3 items-center">
                            <div
                                class="icon-container group border-2 rounded-full w-8 h-8 flex items-center justify-center border-[var(--purple)] transition-colors duration-200 hover:bg-[var(--purple)]">
                                <a href="mailto:<EMAIL>" target="_blank" aria-label="Email BAK"
                                
                                        class="transition-[color] duration-[0.3s] ease-[ease] fa-regular fa-envelope text-lg text-[var(--purple)] group-hover:text-[white]">
                                </a>
                            </div>
                            <a href="mailto:<EMAIL>" target="_blank"
                                class="text-sm hover:underline text-[var(--purple)] font-fors"><EMAIL></a>
                        </div>
                    </div>
                </div>

                <div class="flex justify-end">
                    <button
                        class="relative border-[3px] bg-[#5e2e6a] border-[#5e2e6a] font-bold text-white px-12 py-2 rounded-full transition-colors duration-300 text-sm hover:cursor-pointer hover:border-[#5e2e6a] hover:bg-[#e1005e] font-fors group">
                        <span class="relative z-10">More Info</span>
                    </button>
                </div>
            </div>

            <!-- CARD 3 -->
            <div onclick="openModalBureau(3)"
                class="box-bureau w-full mx-auto flex flex-col justify-between p-6 rounded-lg lg:min-w-l  xl:min-w-lg  2xl:min-w-xl hover:cursor-pointer transition-shadow duration-300 ease-in-out min-h-[200px]h-full"
                data-aos="fade-up-right">

                <div class="flex-grow">
                    <h1 class="font-bold font-fors text-xl mb-5">Biro Administrasi Kemahasiswaan dan Alumni (BAKA)</h1>
                    <div class="space-y-4">
                        <div class="flex gap-3 items-center">
                            <div
                                class="icon-container group border-2 rounded-full w-8 h-8 flex items-center justify-center border-[var(--purple)] transition-colors duration-200 hover:bg-[var(--purple)]">
                                <a href="https://www.instagram.com/baka.pcu" target="_blank"
                                    aria-label="Instagram BAKA"
                                
                                        class="transition-[color] duration-[0.3s] ease-[ease] fa-brands fa-instagram text-lg text-[var(--purple)] group-hover:text-[white]">
                                </a>
                            </div>
                            <a href="https://www.instagram.com/baka.pcu" target="_blank"
                                class="text-sm hover:underline text-[var(--purple)] font-fors">@@baka.pcu</a>
                        </div>
                        <div class="flex gap-3 items-center">
                            <div
                                class="icon-container group border-2 rounded-full w-8 h-8 flex items-center justify-center border-[var(--purple)] transition-colors duration-200 hover:bg-[var(--purple)]">
                                <a href="mailto:<EMAIL>" target="_blank" aria-label="Email BAKA"
                                    
                                        class="transition-[color] duration-[0.3s] ease-[ease] fa-regular fa-envelope text-lg text-[var(--purple)] group-hover:text-[white]">
                                </a>
                            </div>
                            <a href="mailto:<EMAIL>" target="_blank"
                                class="text-sm hover:underline text-[var(--purple)] font-fors"><EMAIL></a>
                        </div>
                        <div class="flex gap-3 items-center">
                            <div
                                class="icon-container group border-2 rounded-full w-8 h-8 flex items-center justify-center border-[var(--purple)] transition-colors duration-200 hover:bg-[var(--purple)]">
                                <a href="baka.petra.ac.id" target="_blank" aria-label="WEBSITE BAKA"
                                    class="transition-[color] duration-[0.3s] ease-[ease] fa-solid fa-globe text-lg text-[var(--purple)] group-hover:text-[white]">
                                </a>
                            </div>
                            <a href="baka.petra.ac.id" target="_blank"
                                class="text-sm hover:underline text-[var(--purple)] font-fors">baka.petra.ac.id</a>
                        </div>
                    </div>
                </div>

                <div class="flex justify-end">
                    <button
                        class="relative border-[3px] bg-[#5e2e6a] border-[#5e2e6a] font-bold text-white px-12 py-2 rounded-full transition-colors duration-300 text-sm hover:cursor-pointer hover:border-[#5e2e6a] hover:bg-[#e1005e] font-fors  group">
                        <span class="relative z-10">More Info</span>
                    </button>
                </div>
            </div>

            {{-- Card 4 --}}
            <div onclick="openModalBureau(4)"
                class="box-bureau w-full mx-auto flex flex-col justify-between p-6 rounded-lg lg:min-w-l  xl:min-w-lg  2xl:min-w-xl hover:cursor-pointer transition-shadow duration-300 ease-in-out min-h-[200px] h-full"
                data-aos="fade-up-left">

                <div class="flex-grow">
                    <h1 class="font-bold font-fors text-xl mb-5">Kantor Kerja Sama dan Urusan Internasional (KUI)</h1>
                    <div class="space-y-4">
                        <div class="flex gap-3 items-center">
                            <div
                                class="icon-container group border-2 rounded-full w-8 h-8 flex items-center justify-center border-[var(--purple)] transition-colors duration-200 hover:bg-[var(--purple)]">
                                <a href="https://io.petra.ac.id" target="_blank" aria-label="WEBSITE IO"
                                
                                        class="transition-[color] duration-[0.3s] ease-[ease] fa-solid fa-globe text-lg text-[var(--purple)] group-hover:text-[white]">
                                </a>
                            </div>
                            <a href="https://io.petra.ac.id" target="_blank"
                                class="text-sm hover:underline [var(--purple)]y-700 font-fors">io.petra.ac.id</a>
                        </div>
                        <div class="flex gap-3 items-center">
                            <div
                                class="icon-container group border-2 rounded-full w-8 h-8 flex items-center justify-center border-[var(--purple)] transition-colors duration-200 hover:bg-[var(--purple)]">
                                <a href="mailto:<EMAIL>" target="_blank" aria-label="Email IO"
                                    
                                        class="transition-[color] duration-[0.3s] ease-[ease] fa-regular fa-envelope text-lg text-[var(--purple)] group-hover:text-[white]">
                                </a>
                            </div>
                            <a href="mailto:<EMAIL>" target="_blank"
                                class="text-sm hover:underline text-[var(--purple)] font-fors"><EMAIL></a>
                        </div>
                    </div>
                </div>

                <div class="flex justify-end">
                    <button
                        class="relative border-[3px] bg-[#5e2e6a] border-[#5e2e6a] font-bold text-white px-12 py-2 rounded-full transition-colors duration-300 text-sm hover:cursor-pointer hover:border-[#5e2e6a] hover:bg-[#e1005e] font-fors  group">
                        <span class="relative z-10">More Info</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Overlay Modal -->
<!-- Modal Template -->
<div id="modal-bureau-info"
    class="fixed inset-0 bg-black/40 backdrop-blur-sm flex items-center justify-center z-50 p-4
    transition-opacity duration-300 opacity-0 pointer-events-none"
    onclick="closeModalBureau(event)">
    <div class="w-full max-w-md rounded-lg p-10 px-10 sm:px-30 relative shadow-lg text-[#221452] lg:min-w-xl xl:max-h-[80vh] xl:min-w-[60vw]
        flex flex-col justify-center items-center
        transform transition-all duration-700 ease-out -translate-y-10 scale-95 opacity-0"
        style="background: #FFCC57; background: linear-gradient(218deg, rgba(255, 204, 87, 1) 10%, rgba(247, 233, 158, 1) 69%);"
        onclick="event.stopPropagation();">

        <h2 id="modal-bureau-title" class="text-3xl font-bold mb-4 text-center font-fors lg:mt-10"></h2>

        <div id="modal-bureau-content"
            class="text-lg text-gray-800 mb-6 leading-relaxed text-center font-fors overflow-y-auto max-h-[calc(100vh-280px)]">
        </div>

        <h3 id="modal-bureau-location" class="text-xl font-bold mb-1 text-center font-fors"></h3>
        <p id="modal-bureau-location-detail" class="text-base text-gray-700 leading-relaxed text-center font-fors">
        </p>

        <div class="flex justify-center mt-4">
            <button onclick="closeModalBureau()"
                class="bg-[#e1005e] font-bold text-xl text-white px-10 py-2 rounded-full hover:bg-[#c20054] transition duration-300 hover:cursor-pointer font-fors">
                Close
            </button>
        </div>

    </div>
</div>

@push('scripts')
    <script>
        //data modal
        //data modal
        const modalBureauData = [{
                id: 1,
                modalBureauTitle: "Biro Administrasi Akademik (BAAk)",
                modalBureauContent: "The Academic Administration Bureau (BAAk) will assist you with academic matters such as <strong> admission information, document requests</strong> and <strong> legalization, study leave</strong>, and other academic-related needs.",
                modalBureauLocation: "Our Location:",
                modalBureauLocationDetail: `EH Building 1st floor
                            </br>Monday - Friday
                            </br>07.30 - 15.30 WIB`
            },
            {
                id: 2,
                modalBureauTitle: "Biro Administrasi Keuangan (BAK)",
                modalBureauContent: "The Financial Administration Bureau (BAK) will assist you with financial matters such as sending <strong> payment reminders via email, handling installment requests,</strong> and<strong> other finance-related concerns.</strong>",
                modalBureauLocation: "Our Location:",
                modalBureauLocationDetail: `EH Building 1st floor
                                 </br>Monday - Friday
                                 </br>07.30 - 15.30 WIB
                                 `
            },
            {
                id: 3,
                modalBureauTitle: "Biro Administrasi Kemahasiswaan dan Alumni (BAKA)",
                modalBureauContent: "Bureau of Student and Alumni Affairs (BAKA) will assist you with <strong>SKKK services, scholarship applications,</strong> and administrative matters related to student activities, such as <strong>submitting activity proposals.</strong>",
                modalBureauLocation: "Our Location:",
                modalBureauLocationDetail: `B Building 2nd floor
                                 </br>Monday - Friday
                                 </br>07.30 - 15.30 WIB
                                 `
            },
            {
                id: 4,
                modalBureauTitle: "Kantor Kerja Sama dan Urusan Internasional (KUI)",
                modalBureauContent: "The Office of Cooperation and International Affairs (KUI) will assist you with consultations regarding your plans to join international programs such as <strong>Student Exchange, Joint/Double Degree,</strong> and <strong>Summer Programs.</strong>",
                modalBureauLocation: "Our Location:",
                modalBureauLocationDetail: `EH Building 1st floor
                                 </br> Monday - Friday
                                 </br> 07.30 - 15.30 WIB
                                 `
            }
        ];

        // Dapatkan referensi elemen modal di luar fungsi agar tidak perlu mencarinya berulang kali
        const modalBureauInfo = document.getElementById('modal-bureau-info');
        const modalBureauContentContainer = modalBureauInfo.querySelector(
            '.w-full.max-w-md'); // Kontainer modal bagian dalamnya

        const modalBureauTitleElem = document.getElementById('modal-bureau-title');
        const modalBureauContentElem = document.getElementById('modal-bureau-content');
        const modalBureauLocationElem = document.getElementById('modal-bureau-location');
        const modalBureauLocationDetailElem = document.getElementById('modal-bureau-location-detail');


        // Event listener untuk mencegah penutupan modal saat mengklik di dalam konten modal itu sendiri
        // Ini harus dijalankan setelah DOMContentLoaded untuk memastikan elemen ada
        window.addEventListener('DOMContentLoaded', () => {
            if (modalBureauContentContainer) {
                modalBureauContentContainer.addEventListener('click', (e) => {
                    e.stopPropagation();
                });
            }
        });

        function openModalBureau(id = null) { // Mengganti nama fungsi
            lenis.stop();
            if (id !== null) {
                const data = modalBureauData.find(item => item.id === id);
                if (data) {
                    modalBureauTitleElem.innerText = data.modalBureauTitle;
                    modalBureauContentElem.innerHTML = data.modalBureauContent;
                    modalBureauLocationElem.innerText = data.modalBureauLocation;
                    modalBureauLocationDetailElem.innerHTML = data.modalBureauLocationDetail;
                }
            }

            // **TRANSISI MASUK:**
            // 1. Hapus 'hidden' untuk membuat modal terlihat (display: flex), tapi masih transparan dan belum di-transform
            modalBureauInfo.classList.remove('hidden');

            // 2. Gunakan requestAnimationFrame untuk memastikan browser telah menerapkan perubahan display
            //    sebelum kita memicu transisi. Ini penting untuk transisi yang mulus dari kondisi 'tersembunyi'.
            requestAnimationFrame(() => {
                // Hapus kelas-kelas yang membuat overlay tidak terlihat
                modalBureauInfo.classList.remove('opacity-0', 'pointer-events-none');
                // Hapus kelas-kelas yang membuat konten modal tersembunyi
                modalBureauContentContainer.classList.remove('-translate-y-10', 'scale-95', 'opacity-0');
            });

            // Tambahkan kelas untuk mencegah scroll body saat modal terbuka
            document.body.classList.add('overflow-hidden');
            document.documentElement.classList.add("no-scroll");
        }

        function closeModalBureau(event) {
            lenis.start();
            // Mencegah penutupan modal ketika mengklik di dalam konten modal itu sendiri
            // atau jika event.target adalah tombol "Close" di dalam modal
            if (event && modalBureauContentContainer.contains(event.target) && event.target !== modalBureauInfo) {
                return;
            }

            // **TRANSISI KELUAR:**
            // 1. Tambahkan kelas-kelas untuk membuat overlay transparan dan konten modal kembali ke kondisi awal tersembunyi
            modalBureauInfo.classList.add('opacity-0', 'pointer-events-none');
            modalBureauContentContainer.classList.add('-translate-y-10', 'scale-95', 'opacity-0');

            // 2. Setelah transisi selesai, sembunyikan modal sepenuhnya
            //    Durasi transisi konten modal adalah 700ms, jadi kita tunggu sedikit lebih lama
            setTimeout(() => {
                modalBureauInfo.classList.add('hidden'); // Kembali ke display: none
                // Hapus kelas untuk mengizinkan scroll body kembali
                document.body.classList.remove('overflow-hidden');
                document.documentElement.classList.remove("no-scroll");
            }, 700); // Sesuaikan dengan durasi transisi konten modal (700ms)
        }
    </script>
@endpush
