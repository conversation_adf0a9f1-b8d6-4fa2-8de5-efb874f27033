<?php


use Illuminate\Support\Facades\Route;
use App\Http\Controllers\MedicineController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\HealthController;
use App\Http\Controllers\MainController;
use App\Http\Controllers\PermissionController;

Route::get('/', [MainController::class, 'landing'])->name('landing');

Route::get('/login', [AuthController::class, 'loginView'])->name('login.view');
Route::get('/auth/google/initiate', [AuthController::class, 'redirectToGoogle'])->name('google.login.initiate');
Route::get('/auth/google/call-back', [AuthController::class, 'handleGoogleCallback'])->name('google.callback');
Route::get('/logout', [AuthController::class, 'logout'])->name('logout');

Route::middleware(['auth.applicant'])->group(function () {
    Route::get('/briefing', [MainController::class, 'viewBriefing'])->name('briefing');

    Route::get('/medicine-form', [MedicineController::class, 'create'])->name('medicine.form.create');
    Route::post('/medicine-form', [MedicineController::class, 'store'])->name('medicine.form.store');
    Route::post('/medicine/remove-date', [MedicineController::class, 'removeMedicineDate'])->name('medicine.remove-date');
    Route::delete('/medicine/{id}/date/{dateToRemove}', [MedicineController::class, 'deleteMedicineDate'])->name('medicine.delete.date');

    Route::get('/kesehatan', [HealthController::class, 'index'])->name('kesehatan.index');
    Route::post('/kesehatan', [HealthController::class, 'store'])->name('kesehatan.store');

    Route::get('/permission', [PermissionController::class, 'index'])->name('permission.index');
    Route::post('/permission', [PermissionController::class, 'store'])->name('permission.store');
    Route::get('/permission/history', [PermissionController::class, 'history'])->name('perizinan.history');
});