@push('styles')
    <style>
        .preserve-3d {
            transform-style: preserve-3d;
        }

        .backface-hidden {
            backface-visibility: hidden;
        }

        .shadow-3xl {
            box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25);
        }

        .neonText {
            color: #fff;
            text-shadow:
                0 0 4px rgba(255, 255, 255, 0.7),
                0 0 7px rgba(230, 240, 246, 0.5),
                0 0 12px rgba(189, 212, 227, 0.4),
                0 0 20px rgba(159, 204, 209, 0.6),
                0 0 30px rgba(159, 204, 209, 0.5);
        }

        .no-scroll {
            overflow: hidden !important;
            height: 100vh !important;
            width: 100vw !important;
        }

        #carousel {
            position: relative;
            overflow: visible;
        }

        .lifted {
            transform: translateY(-60px) scale(1.05);
            z-index: 10;
        }

        .blur-background {
            filter: blur(5px);
            transition: filter 0.1s ease;
        }
    </style>
    {{-- <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet"> --}}
@endpush

<section class="flex flex-col items-center justify-center min-h-screen w-full overflow-x-hidden p-8">
    <div class="w-full max-w-full self-start mb-40 md:mb-25 px-2 md:px-12">
        <h1 class="glowing-title text-white text-4xl sm:text-5xl xl:text-6xl font-bold font-fors text-white mb-2 text-left self-start"
            data-aos="fade-right" data-aos-duration="500" data-aos-easing="ease-out-cubic">
            Timeline
        </h1>
        <h2 class="glowing-title text-white text-lg lg:text-2xl font-medium font-fors text-white text-left self-start"
            data-aos="fade-up" data-aos-duration="800" data-aos-delay="300" data-aos-easing="ease-out-cubic">
            Checkout WGG's timeline and details!
        </h2>
    </div>

    <div id="card-overlay" class="fixed top-0 left-0 inset-0 bg-black/40 z-40 hidden h-screen w-screen"></div>

    <div class="w-full h-full flex flex-col justify-center items-center z-50" data-aos="zoom-in" data-aos-duration="1000" data-aos-delay="700"
            data-aos-easing="ease-out-back">
        <div id="carousel" class="relative w-full z-50 max-w-4xl h-85 md:h-90 mt-6 cursor-grab active:cursor-grabbing"
            style="perspective: 1000px;" >
            <div id="carousel-inner" class="relative w-full h-full preserve-3d font-fors">
            </div>
        </div>

        <div id="dots" class="flex space-x-3">
            
        </div>
        
    </div>
    


    {{-- <div id="custom-modal"
        class="fixed inset-0 bg-transparent backdrop-blur-sm z-50 flex items-center justify-center hidden">
        <div
            class="bg-[linear-gradient(90deg,_#f7e99e,_#ffcc57)] px-4 py-10 rounded-4xl w-[80%] max-w-md h-[440px] text-center shadow-2xl relative flex flex-col justify-center items-center">
            <div class="w-[40%] mx-auto mb-8 items-center justify-center">
                <img id="modal-image" src="{{ asset('assets/flag.png') }}" alt="Flag"
                    class="w-full h-auto max-w-full max-h-[150px] object-contain">
            </div>
            <h2 id="modal-title" class="text-2xl text-[#221452] font-bold italic mb-8"></h2>
            <p id="modal-content" class="text-md text-gray-700 mb-8"></p>
            <button id="close-modal"
                class="bg-[#e1005e] hover:bg-[#c10053] border border-[#5e2e6a] px-8 rounded-4xl py-1 font-fors text-lg cursor-pointer text-white mx-auto">
                Close
            </button>
        </div>
    </div> --}}

</section>

@push('scripts')
    {{-- <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script>
        AOS.init();
    </script> --}}
    <script>
        class CardCarousel {
            constructor() {
                this.currentIndex = 0;
                this.isDragging = false;
                this.startX = 0;
                this.currentX = 0;
                this.dragOffset = 0;
                this.totalCards = 6;
                this.yElevationAmount = 300;
                this.cardIsExpanded = false;



                this.cards = @json($dates);

                this.carousel = document.getElementById('carousel');
                this.carouselInner = document.getElementById('carousel-inner');
                this.dotsContainer = document.getElementById('dots');
                this.closeModalBtn = document.getElementById('close-modal');

                this.init();
            }

            init() {
                this.createCards();
                this.createDots();
                this.bindEvents();
                this.closeModal();
                this.updateCards();
            }

            createCards() {
                this.cards.forEach((card, index) => {
                    const cardElement = document.createElement('div');
                    const isFirstCard = card.id === 1;

                    cardElement.id = `card-${card.id}`;
                    cardElement.className = `card absolute left-1/2 top-1/2 w-56 -ml-28 md:w-72 h-60 md:h-65 md:-ml-36 -mt-40 
              rounded-4xl shadow-md
              bg-[linear-gradient(90deg,_#f7e99e,_#ffcc57)] text-[#221452] text-center font-sans 
              transition-all duration-700 ease-out hover:shadow-3xl`;

                    cardElement.innerHTML = `
        <div class="absolute -top-8 left-1/2 transform -translate-x-1/2 w-22 h-22 bg-[#ffff] rounded-full shadow-lg flex items-center justify-center text-orange-600 text-3xl">
            <img src="${card.image}" class="h-[70%] p-2">
        </div>
        <div class="pt-12 px-6 pb-6 flex flex-col items-center justify-between h-full">
        <div class="h-full mt-4 flex flex-col gap-2 md:gap-6 justify-center">
            <h3 class="text-base md:text-xl font-semibold font-fors italic mb-2">${card.title}</h3>
            <p class="text-xs md:text-sm text-[#211452]/80 mb-4 font-fors text-center">${card.content}</p>
        </div>
            ${isFirstCard 
            ? 
            `<button class="details-btn bg-[var(--pink)] border border-[var(--purple)] transition-colors duration-300 group hover:bg-[var(--purple)] rounded-xl text-white text-xs md:text-sm font-fors font-medium px-6 py-1 shadow-sm hover:shadow-md cursor-pointer" data-card-id="${card.id}">
                Download PDF
                </button>`
            : 
            `<button class="details-btn bg-[var(--pink)] border border-[var(--purple)] transition-colors duration-300 group hover:bg-[var(--purple)] rounded-xl text-white text-xs md:text-sm font-fors font-medium px-6 py-1 shadow-sm hover:shadow-md cursor-pointer" data-card-id="${card.id}">
                Details
                </button>`
            }
        
        </div>`;

                    cardElement.dataset.originalHtml = cardElement.innerHTML;
                    this.carouselInner.appendChild(cardElement);
                });

                document.querySelectorAll('.details-btn').forEach(btn => {
                    btn.addEventListener('click', async (e) => {
                        e.preventDefault();
                        const cardId = e.target.getAttribute('data-card-id');
                        const cardIndex = this.cards.findIndex(card => card.id == cardId);

                        if (cardId == this.cards[0].id) {
                            if (this.currentIndex !== cardIndex) {
                                setTimeout(() => {
                                    this.currentIndex = cardIndex;
                                    this.updateCards();
                                    this.updateDots();
                                }, 200);

                                setTimeout(() => {
                                    const link = document.createElement('a');
                                    link.href = "{{ asset('assets/pdf/sarasehan.pdf') }}";
                                    link.download =
                                        'Pertemuan Orang Tua dan Mahasiswa Baru.pdf';
                                    document.body.appendChild(link);
                                    link.click();
                                    document.body.removeChild(link);
                                }, 800);
                            } else {
                                const link = document.createElement('a');
                                link.href = "{{ asset('assets/pdf/sarasehan.pdf') }}";
                                link.download = 'Pertemuan Orang Tua dan Mahasiswa Baru.pdf';
                                document.body.appendChild(link);
                                link.click();
                                document.body.removeChild(link);
                            }
                            return;
                        }

                        if (this.currentIndex !== cardIndex) {
                            setTimeout(() => {
                                this.currentIndex = cardIndex;
                                this.updateCards();
                                this.updateDots();
                            }, 200);

                            setTimeout(() => {
                                this.expandCard(cardId);
                            }, 800);
                        } else {
                            this.expandCard(cardId);
                        }
                    });
                });
            }

            expandCard(cardId) {
                const card = document.getElementById(`card-${cardId}`);
                const cardData = this.cards.find(card => card.id == cardId);


                if (!card || !cardData) return;


                card.innerHTML = `<div class="flex flex-col mx-4 justify-center items-center h-full -left-1/2">
                <div class="w-[30%] mb-3 mx-auto items-center justify-center">
                    <img src="${cardData.image}" alt="${cardData.title}"
                        class="w-full h-auto max-w-full max-h-[150px] object-contain">
                </div>
                <h2 class="text-md md:text-lg text-[#221452] font-bold italic mb-3">
                    ${cardData.modalTitle}
                </h2>
                <p class="text-xs text-gray-700 mb-5 px-7">
                    ${cardData.modalContent}
                </p>
                <button class="back-btn bg-[var(--pink)] border border-[var(--purple)] hover:bg-[var(--purple)] transition-colors duration-300 group px-5 rounded-3xl py-0.5 font-fors text-sm cursor-pointer text-white mx-auto back-btn">
                    Back
                </button>
            </div>
            </div>
            `;

                document.getElementById('dots').style.opacity = 0;
                card.classList.add('-translate-y-10');
                card.classList.add('scale-105');
                card.classList.add('shadow-xl');
                card.classList.add('z-50')
                document.getElementById('card-overlay').classList.remove('hidden');
                document.documentElement.classList.add("no-scroll");

                if (window.lenis) {
                    window.lenis.stop();
                }

                this.cardIsExpanded = true;

                const allCards = document.querySelectorAll('.card');
                allCards.forEach(c => {
                    if (c.id !== `card-${cardId}`) {
                        c.classList.add('pointer-events-none', 'blur-background');
                    }
                });


                card.querySelector('.back-btn').addEventListener('click', () => {
                    allCards.forEach(c => {
                        c.classList.remove('pointer-events-none', 'blur-background');
                    });
                    this.cardIsExpanded = false;

                    card.innerHTML = card.dataset.originalHtml;
                    document.getElementById('dots').style.opacity = 1;
                    card.classList.remove('-translate-y-10');
                    card.classList.remove('scale-105');
                    card.classList.remove('shadow-xl');
                    document.getElementById('card-overlay').classList.add('hidden');

                    document.documentElement.classList.remove("no-scroll");

                    if (window.lenis) {
                        window.lenis.start();
                    }


                    card.querySelector('.details-btn').addEventListener('click', async (e) => {
                        e.preventDefault();
                        const cardId = e.target.getAttribute('data-card-id');
                        const cardIndex = this.cards.findIndex(card => card.id == cardId);

                        if (cardId == this.cards[0].id) {
                            if (this.currentIndex !== cardIndex) {
                                setTimeout(() => {
                                    this.currentIndex = cardIndex;
                                    this.updateCards();
                                    this.updateDots();
                                }, 200);
                                setTimeout(() => {
                                    window.open("{{ asset('assets/pdf/sarasehan.pdf') }}",
                                        '_blank');
                                }, 800);
                            } else {
                                window.open("{{ asset('assets/pdf/sarasehan.pdf') }}", '_blank');
                            }
                            return;
                        }

                        if (this.currentIndex !== cardIndex) {
                            setTimeout(() => {
                                this.currentIndex = cardIndex;
                                this.updateCards();
                                this.updateDots();
                            }, 200);
                            setTimeout(() => {
                                this.expandCard(cardId);
                            }, 800);
                        } else {
                            this.expandCard(cardId);
                        }
                    });
                });

                const overlay = document.getElementById('card-overlay');
                const closeCard = () => {
                    allCards.forEach(c => {
                        c.classList.remove('pointer-events-none', 'blur-background');
                    });
                    this.cardIsExpanded = false;

                    card.innerHTML = card.dataset.originalHtml;
                    card.classList.remove('-translate-y-10', 'scale-105', 'shadow-xl', 'z-50');
                    overlay.classList.add('hidden');
                    document.documentElement.classList.remove("no-scroll");

                    if (window.lenis) {
                        window.lenis.start();
                    }

                    card.querySelector('.details-btn').addEventListener('click', async (e) => {
                        e.preventDefault();
                        const cardId = e.target.getAttribute('data-card-id');
                        const cardIndex = this.cards.findIndex(card => card.id == cardId);

                        if (cardId == this.cards[0].id) {
                            if (this.currentIndex !== cardIndex) {
                                setTimeout(() => {
                                    this.currentIndex = cardIndex;
                                    this.updateCards();
                                    this.updateDots();
                                }, 200);
                                setTimeout(() => {
                                    window.open("{{ asset('assets/pdf/sarasehan.pdf') }}",
                                        '_blank');
                                }, 800);
                            } else {
                                window.open("{{ asset('assets/pdf/sarasehan.pdf') }}", '_blank');
                            }
                            return;
                        }

                        if (this.currentIndex !== cardIndex) {
                            setTimeout(() => {
                                this.currentIndex = cardIndex;
                                this.updateCards();
                                this.updateDots();
                            }, 200);
                            setTimeout(() => {
                                this.expandCard(cardId);
                            }, 800);
                        } else {
                            this.expandCard(cardId);
                        }
                    });
                };

                card.querySelector('.back-btn').addEventListener('click', closeCard);

                overlay.addEventListener('click', (e) => {
                    if (e.target === overlay) {
                        closeCard();
                    }
                });

            }


            closeModal() {
                if (!this.closeModalBtn) return;

                this.closeModalBtn.addEventListener('click', () => {
                    this.hideModal();
                });
            }

            // showModal(cardId) {
            //     const modal = document.getElementById('custom-modal');
            //     const card = document.getElementById(`card-${cardId}`);
            //     if (card) card.style.display = 'none';

            //     const selectedCard = this.cards.find(c => c.id === parseInt(cardId));
            //     if (selectedCard) {
            //         document.getElementById('modal-title').innerHTML = selectedCard.modalTitle ?? selectedCard.title;
            //         document.getElementById('modal-content').innerHTML = selectedCard.modalContent ?? selectedCard
            //             .content;
            //         const modalImg = document.getElementById('modal-image');
            //         modalImg.src = selectedCard.image;
            //         modalImg.alt = selectedCard.title;
            //     }

            //     modal.classList.remove('hidden');
            //     document.documentElement.classList.add("no-scroll");
            //     this.hiddenCardId = cardId;

            // }

            hideModal() {
                const modal = document.getElementById('custom-modal');
                modal.classList.add('hidden');

                if (this.hiddenCardId) {
                    const card = document.getElementById(`card-${this.hiddenCardId}`);
                    if (card) card.style.display = '';
                    this.hiddenCardId = null;
                }
                document.documentElement.classList.remove("no-scroll");
            }

            createDots() {
                for (let i = 0; i < this.totalCards; i++) {
                    const dot = document.createElement('button');
                    dot.className = `w-3 h-3 rounded-full transition-all duration-300 ${
                        i === this.currentIndex 
                            ? 'bg-white scale-125 shadow-lg' 
                            : 'bg-white/40 hover:bg-white/60'
                    }`;
                    dot.addEventListener('click', () => this.goToSlide(i));
                    this.dotsContainer.appendChild(dot);
                }
            }

            updateDots() {
                const dots = this.dotsContainer.children;
                for (let i = 0; i < dots.length; i++) {
                    dots[i].className = `w-3 h-3 rounded-full transition-all duration-300 ${
                        i === this.currentIndex 
                            ? 'bg-white scale-125 shadow-lg' 
                            : 'bg-white/40 hover:bg-white/60'
                    }`;
                }
            }

            getCardStyle(index) {
                const angle = ((index - this.currentIndex) * 360) / this.totalCards;
                const radius = window.innerWidth >= 1440 ? 300 : 240;
                const dragRotation = this.isDragging ? (this.dragOffset * 0.3) : 0;
                const finalAngle = angle + dragRotation;

                const angleRad = (finalAngle * Math.PI) / 180;
                const cosAngle = Math.cos(angleRad);

                const translateX = Math.sin(angleRad) * radius;
                const translateZ = cosAngle * radius;
                const scale = cosAngle * 0.3 + 0.7;
                const opacity = cosAngle * 0.4 + 0.6;
                const translateY = ((1 - cosAngle) / 2) * -this.yElevationAmount;

                return {
                    transform: `translateX(${translateX}px) translateY(${translateY}px) translateZ(${translateZ}px) scale(${scale})`,
                    opacity: opacity,
                    zIndex: Math.round(translateZ)
                };
            }

            updateCards() {
                const cardElements = this.carouselInner.children;
                for (let i = 0; i < cardElements.length; i++) {
                    const style = this.getCardStyle(i);
                    const card = cardElements[i];

                    card.style.transform = style.transform;
                    card.style.opacity = style.opacity;
                    card.style.zIndex = style.zIndex;

                    // Remove transition during drag
                    if (this.isDragging) {
                        card.style.transition = 'none';
                    } else {
                        card.style.transition = 'all 0.7s ease-out';
                    }
                }
            }

            handleStart(clientX) {
                if (this.cardIsExpanded) return;
                this.isDragging = true;
                this.startX = clientX;
                this.currentX = clientX;
                this.carousel.style.cursor = 'grabbing';
            }

            handleMove(clientX) {
                if (this.cardIsExpanded) return;
                if (!this.isDragging) return;
                const diff = clientX - this.startX;
                this.dragOffset = diff;
                this.currentX = clientX;
                this.updateCards();
            }

            handleEnd() {
                if (this.cardIsExpanded) return;
                if (!this.isDragging) return;
                const threshold = 50;
                const diff = this.currentX - this.startX;
                if (Math.abs(diff) > threshold) {
                    if (diff > 0) {
                        // Swipe right - go to previous
                        this.currentIndex = (this.currentIndex - 1 + this.totalCards) % this.totalCards;
                    } else {
                        // Swipe left - go to next
                        this.currentIndex = (this.currentIndex + 1) % this.totalCards;
                    }
                }
                this.isDragging = false;
                this.dragOffset = 0;
                this.startX = 0;
                this.currentX = 0;
                this.carousel.style.cursor = 'grab';
                this.updateCards();
                this.updateDots();
            }
            goToSlide(index) {
                if (!this.isDragging) {
                    this.currentIndex = index;
                    this.updateCards();
                    this.updateDots();
                }
            }

            bindEvents() {
                // Mouse events
                this.carousel.addEventListener('mousedown', (e) => {
                    e.preventDefault();
                    this.handleStart(e.clientX);
                });

                document.addEventListener('mousemove', (e) => {
                    this.handleMove(e.clientX);
                });

                document.addEventListener('mouseup', () => {
                    this.handleEnd();
                });

                // Touch events
                this.carousel.addEventListener('touchstart', (e) => {
                    this.handleStart(e.touches[0].clientX);
                }, {
                    passive: true
                });

                document.addEventListener('touchmove', (e) => {
                    this.handleMove(e.touches[0].clientX);
                }, {
                    passive: true
                });

                document.addEventListener('touchend', () => {
                    this.handleEnd();
                }, {
                    passive: true
                });

                // Prevent context menu on long press
                this.carousel.addEventListener('contextmenu', (e) => {
                    e.preventDefault();
                });
            }
        }

        document.addEventListener('DOMContentLoaded', () => {
            new CardCarousel();
        });
    </script>
@endpush
