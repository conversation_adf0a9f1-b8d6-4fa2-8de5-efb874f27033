<div id="beyoPopup" class="fixed bottom-2 right-2 sm:bottom-4 sm:right-4 z-50 notification-popup">
    <div class="relative bg-[linear-gradient(90deg,_var(--light-yellow),_var(--yellow))] text-[var(--dark-purple)] p-5 rounded-3xl shadow-2xl max-w-sm notification-shadow">
        <button onclick="closeBeyoPopup()" class="absolute top-3 right-3 w-8 h-8 flex items-center justify-center text-[var(--dark-purple)] hover:bg-opacity-10 rounded-full notification-close-btn cursor-pointer">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
        </button>
        
        <h3 class="font-bold text-md sm:text-lg mb-2 font-fors">Hey, make sure you're signed in!</h3>
            <p class="text-sm mb-4 leading-relaxed font-fors">
                Remember to fill out your Medical & Dietary Form! You can do so by signing in.
            </p>
            
            <div class="flex justify-end">
                <button onclick="handleSignIn()" class="border-[3px] bg-[var(--purple)] border-[var(--purple)] font-bold text-white px-8 py-1 rounded-full transition duration-300 text-xs hover:cursor-pointer hover:border-[var(--purple)] hover:bg-[var(--pink)] font-fors group">
                    Sign In
                </button>
            </div>
    </div>
</div>